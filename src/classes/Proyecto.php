<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;
use InvalidArgumentException; // Para validaciones

class Proyecto
{
    // --- Atributos ---
    private ?int $id                = null;  // Usar null como default para nuevos objetos sin ID
    private ?string $descripcion    = null;
    private ?int $estado            = null;
    private ?string $fecha_creacion = null;
    private ?string $fecha_inicio   = null;
    private ?int $id_aliado_comercial = null; // Nuevo campo

    // Document storage fields (managed separately from CRUD operations)
    private ?string $doc_levantamiento_necesidades = null;
    private ?string $doc_cotizacion = null;
    private ?string $doc_presentacion = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Proyecto.
     */
    public function __construct()
    {
        $this->id             = null;  // Default null
        $this->descripcion    = null;
        $this->estado         = 1;     // Estado activo por defecto
        $this->fecha_creacion = null;  // Will be set by DB on insert
        $this->fecha_inicio   = null;
        $this->id_aliado_comercial = null; // Inicializar nuevo campo

        // Initialize document storage fields
        $this->doc_levantamiento_necesidades = null;
        $this->doc_cotizacion = null;
        $this->doc_presentacion = null;
    }

    /**
     * Método estático para construir un objeto Proyecto desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del proyecto.
     * @return self Instancia de Proyecto.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                 = new self();
            $objeto->id             = isset($resultado['id']) ? (int)$resultado['id'] : null;       // Default null
            $objeto->descripcion    = $resultado['descripcion'] ?? null;
            $objeto->estado         = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            $objeto->fecha_creacion = $resultado['fecha_creacion'] ?? null;                         // Cargar fecha de DB si existe
            $objeto->fecha_inicio   = $resultado['fecha_inicio'] ?? null;                           // Load new field
            $objeto->id_aliado_comercial = isset($resultado['id_aliado_comercial']) ? (int)$resultado['id_aliado_comercial'] : null; // Cargar nuevo campo

            // Load document storage fields
            $objeto->doc_levantamiento_necesidades = $resultado['doc_levantamiento_necesidades'] ?? null;
            $objeto->doc_cotizacion = $resultado['doc_cotizacion'] ?? null;
            $objeto->doc_presentacion = $resultado['doc_presentacion'] ?? null;

            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir Proyecto: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos ---

    /**
     * Obtiene un proyecto por su ID.
     *
     * @param int $id ID del proyecto.
     * @param PDO $conexion Conexión PDO.
     * @return self|null Objeto Proyecto o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        if ($id <= 0) {
             throw new InvalidArgumentException("ID de Proyecto inválido.");
        }
        try {
            $query = <<<SQL
            SELECT * FROM proyectos WHERE id = :id LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Proyecto (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de proyectos activos.
     *
     * @param PDO $conexion Conexión PDO.
     * @return array Array de objetos Proyecto.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
            	*
            FROM proyectos
            WHERE
            	estado = 1
            ORDER BY
            	 descripcion
            SQL; 

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Proyectos: " . $e->getMessage());
        }
    }

    /**
     * Obtiene el proyecto activo asociado a un Aliado Comercial.
     *
     * @param int $id_aliado ID del Aliado Comercial.
     * @param PDO $conexion Conexión PDO.
     * @return self|null Objeto Proyecto o null si no se encuentra.
     * @throws Exception Si hay error en DB o ID inválido.
     */
    public static function getByAliadoComercialId(int $id_aliado, PDO $conexion): ?self
    {
        if ($id_aliado <= 0) {
            throw new InvalidArgumentException("ID de Aliado Comercial inválido.");
        }
        try {
            $query = <<<SQL
            SELECT * FROM proyectos
            WHERE id_aliado_comercial = :id_aliado AND estado = 1
            LIMIT 1 -- Ensure only one is returned
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id_aliado", $id_aliado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Proyectos por Aliado (ID Aliado: $id_aliado): " . $e->getMessage());
        }
    }

     /**
     * Guarda (inserta o actualiza) el proyecto en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     * @return bool True si la operación fue exitosa, False en caso contrario.
     * @throws Exception Si faltan datos requeridos o hay error en DB.
     */
    public function guardar(PDO $conexion): bool
    {
        // --- Check for existing project if inserting ---
        if ($this->getId() === null && $this->getIdAliadoComercial() !== null) {
            $existingProject = self::getByAliadoComercialId($this->getIdAliadoComercial(), $conexion);
            if ($existingProject !== null) {
                throw new Exception("Este Aliado Comercial ya tiene un proyecto asociado (ID: {$existingProject->getId()}). No se puede crear uno nuevo.");
            }
        }
        // Validar campos obligatorios antes de intentar guardar
        $this->validarDatosObligatorios();
	    
	    date_default_timezone_set('America/Bogota');
		
	    // Decidir si insertar o actualizar basado en el ID
        if ($this->getId() !== null && $this->getId() > 0) {
            return $this->_update($conexion);
        } else {
            $newId = $this->_insert($conexion);
            if ($newId !== false) {
                $this->setId($newId); // Actualizar el ID del objeto
                return true;
            }
            return false;
        }
    }


    /**
     * Inserta un nuevo proyecto en la base de datos. (Método privado)
     *
     * @param PDO $conexion Conexión PDO.
     * @return int|false El ID del nuevo proyecto creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    private function _insert(PDO $conexion): int|false // Renombrado de crear a _insert
    {
        // Validación ya hecha en guardar()
        // --- Double check for existing project (redundant if check in guardar() is reliable, but safe) ---
        if ($this->getIdAliadoComercial() !== null) {
            $existingProject = self::getByAliadoComercialId($this->getIdAliadoComercial(), $conexion);
            if ($existingProject !== null) {
                // This specific exception might be better handled in guardar() to provide user feedback
                throw new Exception("Intento de inserción duplicada: El Aliado Comercial ya tiene un proyecto.");
            }
        }
        // fecha_creacion is now handled by DB DEFAULT CURRENT_TIMESTAMP
        try {
            $query = <<<SQL
            INSERT INTO proyectos (descripcion, estado, fecha_inicio, id_aliado_comercial)
            VALUES (:descripcion, :estado, :fecha_inicio, :id_aliado_comercial)
            SQL;

            $statement = $conexion->prepare($query);

            // Bind values from the object
            $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT); // Default a 1 si es null
            $statement->bindValue(':fecha_inicio', $this->getFechaInicio(), PDO::PARAM_STR); // Bind new field
            $statement->bindValue(':id_aliado_comercial', $this->getIdAliadoComercial(), ($this->getIdAliadoComercial() === null || $this->getIdAliadoComercial() <= 0) ? PDO::PARAM_NULL : PDO::PARAM_INT); // Bind nuevo campo (handle null or invalid)

            $success = $statement->execute();

            return $success ? (int)$conexion->lastInsertId() : false;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear proyecto: " . $e->getMessage());
        }
    }

    /**
     * Modifica la descripción y estado de un proyecto existente. (Método privado)
     *
     * @param PDO $conexion Conexión PDO.
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si la descripción está vacía o si ocurre un error de base de datos.
     */
    private function _update(PDO $conexion): bool // Cambiado de static, renombrado, parámetros eliminados
    {
         if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("Se requiere un ID válido para actualizar el Proyecto.");
        }
        // Validación ya hecha en guardar()

        try {
            $query = <<<SQL
            UPDATE proyectos
            SET
                descripcion  = :descripcion,
                fecha_inicio = :fecha_inicio
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
            $statement->bindValue(':fecha_inicio', $this->getFechaInicio(), PDO::PARAM_STR); // Bind new field
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar proyecto (ID: {$this->getId()}): " . $e->getMessage());
        }
    }


    /**
     * Desactiva un proyecto estableciendo su estado a 0.
     *
     * @param int $id ID del proyecto a desactivar.
     * @param PDO $conexion Conexión PDO.
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
         if ($id <= 0) {
            throw new InvalidArgumentException("ID de Proyecto inválido.");
        }
        try {
            $query = <<<SQL
            UPDATE proyectos SET estado = 0 WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar proyecto (ID: $id): " . $e->getMessage());
        }
    }

     /**
     * Valida que los campos obligatorios no estén vacíos.
     * Lanza una excepción si falta alguno.
     * @throws InvalidArgumentException
     */
    private function validarDatosObligatorios(): void
    {
        if (empty(trim($this->getDescripcion() ?? ''))) {
            throw new InvalidArgumentException("La descripción es obligatoria.");
        }
        // Add validation for fecha_inicio if it's mandatory
        if (empty($this->getFechaInicio())) {
            throw new InvalidArgumentException("La fecha de inicio es obligatoria.");
        }
    }

    // --- Getters y Setters ---

    public function getId()        : ?int { return $this->id; }
    public function setId(?int $id): self { $this->id = $id; return $this; }

    public function getDescripcion()                    : ?string { return $this->descripcion; }
    public function setDescripcion(?string $descripcion): self { $this->descripcion = $descripcion; return $this; }

    public function getEstado()            : ?int { return $this->estado; }
    public function setEstado(?int $estado): self { $this->estado = ($estado === 0) ? 0 : 1; return $this; }  // Asegurar 0 o 1

    public function getFechaCreacion()                       : ?string { return $this->fecha_creacion; }
    public function setFechaCreacion(?string $fecha_creacion): self { $this->fecha_creacion = $fecha_creacion; return $this; }

    public function getFechaInicio()                     : ?string { return $this->fecha_inicio; }
    public function setFechaInicio(?string $fecha_inicio): self { $this->fecha_inicio = $fecha_inicio; return $this; }

    public function getIdAliadoComercial()                             : ?int { return $this->id_aliado_comercial; }
    public function setIdAliadoComercial(?int $id_aliado_comercial): self { $this->id_aliado_comercial = $id_aliado_comercial; return $this; }

    // Document storage fields getters (no setters as these are managed separately)
    public function getDocLevantamientoNecesidades(): ?string { return $this->doc_levantamiento_necesidades; }
    public function getDocCotizacion(): ?string { return $this->doc_cotizacion; }
    public function getDocPresentacion(): ?string { return $this->doc_presentacion; }

    // --- Métodos adicionales ---

    /**
     * Verifica si el proyecto está activo.
     * @return bool
     */
    public function isActivo(): bool
    {
        return $this->estado === 1;
    }
}
