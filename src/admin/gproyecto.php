<?php

// Iniciar sesión si es necesario
use App\classes\Proyecto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en gproyecto.php.");
	$_SESSION['flash_message_error'] = 'Error crítico: No se pudo conectar a la base de datos.';
	header('Location: lproyectos');
	exit;
}

#region region Initialize Variables
$proyecto = null;
$proyectoId = null;
$error_display = 'none';
$error_text = '';
$success_display = 'none';
$success_text = '';
#endregion Initialize Variables

#region region Session Flash Messages
if (isset($_SESSION['flash_message_success'])) {
	$success_display = 'show';
	$success_text = $_SESSION['flash_message_success'];
	unset($_SESSION['flash_message_success']);
}

if (isset($_SESSION['flash_message_error'])) {
	$error_display = 'show';
	$error_text = $_SESSION['flash_message_error'];
	unset($_SESSION['flash_message_error']);
}
#endregion Session Flash Messages

#region region Project ID Validation and Loading
// Check if project ID is provided via session (secure parameter passing)
if (isset($_SESSION['gestion_proyecto_id'])) {
	$proyectoId = filter_var($_SESSION['gestion_proyecto_id'], FILTER_VALIDATE_INT);
	
	if ($proyectoId && $proyectoId > 0) {
		try {
			$proyecto = Proyecto::get($proyectoId, $conexion);
			
			if (!$proyecto) {
				$_SESSION['flash_message_error'] = "Error: No se encontró el proyecto especificado.";
				unset($_SESSION['gestion_proyecto_id']);
				header('Location: lproyectos');
				exit;
			}
			
			// Verify project is active
			if (!$proyecto->isActivo()) {
				$_SESSION['flash_message_error'] = "Error: El proyecto especificado no está activo.";
				unset($_SESSION['gestion_proyecto_id']);
				header('Location: lproyectos');
				exit;
			}
			
		} catch (Exception $e) {
			error_log("Error loading project in gproyecto.php: " . $e->getMessage());
			$_SESSION['flash_message_error'] = "Error al cargar el proyecto: " . $e->getMessage();
			unset($_SESSION['gestion_proyecto_id']);
			header('Location: lproyectos');
			exit;
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de proyecto inválido.";
		unset($_SESSION['gestion_proyecto_id']);
		header('Location: lproyectos');
		exit;
	}
} else {
	// No project ID provided
	$_SESSION['flash_message_error'] = "Error: No se especificó un proyecto para gestionar.";
	header('Location: lproyectos');
	exit;
}
#endregion Project ID Validation and Loading

#region region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Handle any POST actions here in the future
	// For now, just redirect back to prevent form resubmission
	header('Location: gestion-proyecto');
	exit;
}
#endregion Handle POST Actions

// Load the view
require_once __ROOT__ . '/views/admin/gproyecto.view.php';

?>
